name: time_tracker_flutter
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  sqflite: ^2.3.3+1
  path: ^1.9.0
  logger: ^2.4.0
  intl: ^0.20.2
  csv: ^6.0.0
  path_provider: ^2.1.5
  share_plus: ^11.0.0
  qr_flutter: ^4.1.0
  shared_preferences: ^2.2.3
  crypto: ^3.0.3
  file_picker: ^10.1.8
  data_table_2: ^2.6.0
  location: ^8.0.1
  flutter_map: ^8.1.1
  latlong2: ^0.9.0
  geolocator: ^14.0.2
  sensors_plus: ^6.1.1
  battery_plus: ^6.0.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management
  provider: ^6.1.2
  flutter_riverpod: ^2.5.1

  # Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI components
  flutter_slidable: ^4.0.0
  fl_chart: ^0.66.2

  # Utilities
  uuid: ^4.5.1
  flutter_local_notifications: ^19.4.0
  vibration: ^3.1.3
  flutter_foreground_task: ^9.1.0

  # Cloud storage
  supabase_flutter: ^2.3.4

  # Encryption
  encrypt: ^5.0.3

  # QR code
  mobile_scanner: ^7.0.1
  http: ^1.4.0

  # PDF generation
  pdf: ^3.11.1
  pdfrx: ^2.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

  # Code generation
  build_runner: ^2.5.4
  hive_generator: ^2.0.1
  mockito: ^5.4.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - linux/runner/resources/app_icon.svg
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
