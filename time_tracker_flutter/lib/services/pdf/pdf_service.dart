import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/models/pdf/template_models.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/services/pdf/template_engine.dart';

enum InvoiceTemplate {
  professional,
  minimal,
  detailed,
}

class BusinessInfo {
  final String name;
  final String? email;
  final String? phone;
  final Address? address;
  final String? website;
  final String? taxId;
  final Uint8List? logoData;

  const BusinessInfo({
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.website,
    this.taxId,
    this.logoData,
  });
}

class PDFService {
  final DatabaseService _databaseService;

  PDFService({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService();

  Future<Uint8List> generateInvoicePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    PDFTemplate? customTemplate,
  }) async {
    try {
      final font = await _loadFont();
      final boldFont = await _loadBoldFont();
      final invoiceLanguage = language ?? invoice.locale;

      // Convert invoice locale string to Locale object for proper formatting
      final effectiveLocale = locale ?? InvoiceLocalizationService.getLocaleFromLanguage(invoice.locale);

      // Debug: Print which path is being taken
      if (customTemplate != null) {
        print('NewPDFService: Using custom template "${customTemplate.name}" with ${customTemplate.sections.length} sections');
        return await _generateWithCustomTemplate(
          customTemplate,
          invoice,
          client,
          businessInfo,
          timeEntries,
          effectiveLocale,
          font,
          boldFont,
        );
      } else {
        print('NewPDFService: Using legacy template system for template: $template');
        return await _generateWithLegacyTemplate(
          template,
          invoice,
          client,
          businessInfo,
          timeEntries,
          effectiveLocale,
          invoiceLanguage,
          font,
          boldFont,
        );
      }
    } catch (e) {
      throw PDFGenerationException('Failed to generate PDF: $e');
    }
  }

  Future<Uint8List> _generateWithCustomTemplate(
    PDFTemplate template,
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    final data = _createPDFData(invoice, client, businessInfo, timeEntries, locale);
    final document = await TemplateEngine.renderTemplate(template, data, font, boldFont);
    return await document.save();
  }

  Future<Uint8List> _generateWithLegacyTemplate(
    InvoiceTemplate template,
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String language,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    final pdf = pw.Document();

    switch (template) {
      case InvoiceTemplate.professional:
        await _addProfessionalTemplate(
          pdf,
          invoice,
          client,
          businessInfo,
          font,
          boldFont,
          timeEntries,
          locale,
          language,
        );
        break;
      case InvoiceTemplate.minimal:
        await _addMinimalTemplate(
          pdf,
          invoice,
          client,
          businessInfo,
          font,
          boldFont,
          timeEntries,
          locale,
          language,
        );
        break;
      case InvoiceTemplate.detailed:
        await _addDetailedTemplate(
          pdf,
          invoice,
          client,
          businessInfo,
          font,
          boldFont,
          timeEntries,
          locale,
          language,
        );
        break;
    }

    return await pdf.save();
  }

  PDFData _createPDFData(
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    List<TimeEntry>? timeEntries,
    Locale? locale,
  ) {
    return PDFData(
      invoice: {
        'invoiceNumber': invoice.invoiceNumber,
        'issueDate': _formatDate(invoice.issueDate, locale),
        'dueDate': invoice.dueDate != null ? _formatDate(invoice.dueDate!, locale) : null,
        'subtotal': invoice.subtotal,
        'taxAmount': invoice.taxAmount,
        'taxRate': invoice.taxRate,
        'total': invoice.total,
        'currency': invoice.currency,
        'status': invoice.status.toString(),
        'notes': invoice.notes,
        'additionalItems': invoice.additionalItems.map((item) => {
          'description': item.description,
          'quantity': item.quantity,
          'rate': item.rate,
          'amount': item.amount,
        }).toList(),
      },
      client: {
        'name': client.name,
        'email': client.email,
        'phone': client.phone,
        'address': client.address?.formattedAddress,
        'taxId': client.taxId,
      },
      businessInfo: {
        'name': businessInfo.name,
        'email': businessInfo.email,
        'phone': businessInfo.phone,
        'address': businessInfo.address?.formattedAddress,
        'website': businessInfo.website,
        'taxId': businessInfo.taxId,
        'logoData': businessInfo.logoData?.toList(),
      },
      timeEntries: timeEntries?.map((entry) => {
        'project': entry.projectId,
        'date': entry.date,
        'hours': _parseDuration(entry.duration),
        'rate': 0.0, // Default rate since TimeEntry doesn't have hourlyRate
        'amount': _parseDuration(entry.duration) * 0.0, // Default calculation
      }).toList() ?? [],
    );
  }

  Future<String> generateAndSavePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    String? customFileName,
    String? customDirectory,
    Function(double)? onProgress,
    PDFTemplate? customTemplate,
  }) async {
    try {
      onProgress?.call(0.1);

      final pdfData = await generateInvoicePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
        customTemplate: customTemplate,
      );

      onProgress?.call(0.7);

      final directory = customDirectory != null
          ? Directory(customDirectory)
          : await getApplicationDocumentsDirectory();

      final fileName = customFileName ??
          'invoice_${invoice.invoiceNumber.replaceAll(RegExp(r'[^\w\-_]'), '_')}.pdf';
      final file = File('${directory.path}/$fileName');

      await file.writeAsBytes(pdfData);
      onProgress?.call(1.0);
      return file.path;
    } catch (e) {
      throw PDFGenerationException('Failed to save PDF: $e');
    }
  }

  Future<void> shareInvoicePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Function(double)? onProgress,
    PDFTemplate? customTemplate,
  }) async {
    try {
      onProgress?.call(0.1);

      final filePath = await generateAndSavePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
        onProgress: onProgress,
        customTemplate: customTemplate,
      );

      onProgress?.call(0.9);

      await Share.share(
        'Please find attached invoice ${invoice.invoiceNumber} for ${client.name}.\n$filePath',
        subject: 'Invoice ${invoice.invoiceNumber}',
      );

      onProgress?.call(1.0);
    } catch (e) {
      throw PDFGenerationException('Failed to share PDF: $e');
    }
  }

  Future<pw.Font> _loadFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to Helvetica if TTF loading fails
      return pw.Font.helvetica();
    }
  }

  Future<pw.Font> _loadBoldFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to Helvetica Bold if TTF loading fails
      return pw.Font.helveticaBold();
    }
  }

  String _formatCurrency(double amount, String currency, Locale? locale) {
    return CurrencyService.formatCurrency(amount, currency, locale);
  }

  String _formatDate(DateTime date, Locale? locale) {
    return LocaleDateUtils.formatDate(date, locale);
  }

  double _parseDuration(String? duration) {
    if (duration == null) return 0.0;

    // Parse duration string like "2h 30m" or "150m"
    final hoursMatch = RegExp(r'(\d+)h').firstMatch(duration);
    final minutesMatch = RegExp(r'(\d+)m').firstMatch(duration);

    double hours = 0.0;
    double minutes = 0.0;

    if (hoursMatch != null) {
      hours = double.tryParse(hoursMatch.group(1)!) ?? 0.0;
    }

    if (minutesMatch != null) {
      minutes = double.tryParse(minutesMatch.group(1)!) ?? 0.0;
    }

    return hours + (minutes / 60.0);
  }

  Future<void> _addProfessionalTemplate(
    pw.Document pdf,
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    pw.Font font,
    pw.Font boldFont,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String language,
  ) async {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            _buildHeader(businessInfo, boldFont, font, language),
            pw.SizedBox(height: 30),
            _buildInvoiceTitle(invoice, boldFont, locale, language),
            pw.SizedBox(height: 20),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  child: _buildClientInfo(client, boldFont, font, language),
                ),
                pw.SizedBox(width: 40),
                pw.Expanded(
                  child: _buildInvoiceInfo(invoice, boldFont, font, locale, language),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            _buildLineItemsTable(invoice, timeEntries, boldFont, font, locale, language),
            pw.SizedBox(height: 20),
            _buildTotalsSection(invoice, boldFont, font, locale, language),
            pw.SizedBox(height: 30),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildNotesSection(invoice.notes!, boldFont, font, language),
            pw.Spacer(),
            _buildFooter(businessInfo, font, language),
          ];
        },
      ),
    );
  }

  Future<void> _addMinimalTemplate(
    pw.Document pdf,
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    pw.Font font,
    pw.Font boldFont,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String language,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    businessInfo.name,
                    style: pw.TextStyle(font: boldFont, fontSize: 18),
                  ),
                  pw.Text(
                    InvoiceLocalizationService.getTranslation('invoice', language),
                    style: pw.TextStyle(font: boldFont, fontSize: 24),
                  ),
                ],
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${InvoiceLocalizationService.getTranslation('invoice_number', language)}${invoice.invoiceNumber}',
                style: pw.TextStyle(font: boldFont, fontSize: 14),
              ),
              pw.Text(
                '${InvoiceLocalizationService.getTranslation('date', language)}: ${_formatDate(invoice.issueDate, locale)}',
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                InvoiceLocalizationService.getTranslation('bill_to', language),
                style: pw.TextStyle(font: boldFont, fontSize: 12),
              ),
              pw.Text(
                client.name,
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              if (client.address != null)
                pw.Text(
                  client.address!.formattedAddress,
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
              pw.SizedBox(height: 20),
              _buildSimpleLineItems(invoice, timeEntries, boldFont, font, locale, language),
              pw.SizedBox(height: 20),
              pw.Align(
                alignment: pw.Alignment.centerRight,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text(
                      '${InvoiceLocalizationService.getTranslation('subtotal', language)}: ${_formatCurrency(invoice.subtotal, invoice.currency, locale)}',
                      style: pw.TextStyle(font: font, fontSize: 12),
                    ),
                    if (invoice.taxAmount > 0)
                      pw.Text(
                        '${InvoiceLocalizationService.getTranslation('tax', language)} (${invoice.taxRate.toStringAsFixed(1)}%): ${_formatCurrency(invoice.taxAmount, invoice.currency, locale)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    pw.Container(
                      padding: const pw.EdgeInsets.only(top: 5),
                      decoration: const pw.BoxDecoration(
                        border: pw.Border(
                          top: pw.BorderSide(width: 1),
                        ),
                      ),
                      child: pw.Text(
                        '${InvoiceLocalizationService.getTranslation('total', language)}: ${_formatCurrency(invoice.total, invoice.currency, locale)}',
                        style: pw.TextStyle(font: boldFont, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _addDetailedTemplate(
    pw.Document pdf,
    Invoice invoice,
    Client client,
    BusinessInfo businessInfo,
    pw.Font font,
    pw.Font boldFont,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String language,
  ) async {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            _buildDetailedHeader(businessInfo, boldFont, font, language),
            pw.SizedBox(height: 30),
            _buildDetailedInvoiceTitle(invoice, boldFont, font, locale, language),
            pw.SizedBox(height: 20),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  child: _buildDetailedClientInfo(client, boldFont, font, language),
                ),
                pw.SizedBox(width: 40),
                pw.Expanded(
                  child: _buildDetailedInvoiceInfo(invoice, boldFont, font, locale, language),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            if (timeEntries != null && timeEntries.isNotEmpty)
              _buildDetailedTimeEntries(timeEntries, invoice, boldFont, font, locale, language),
            if (invoice.additionalItems.isNotEmpty)
              _buildDetailedLineItems(invoice, boldFont, font, locale, language),
            pw.SizedBox(height: 20),
            _buildDetailedTotalsSection(invoice, boldFont, font, locale, language),
            pw.SizedBox(height: 30),
            _buildPaymentTerms(invoice, boldFont, font, locale, language),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildNotesSection(invoice.notes!, boldFont, font, language),
            pw.Spacer(),
            _buildDetailedFooter(businessInfo, font, language),
          ];
        },
      ),
    );
  }

  pw.Widget _buildHeader(BusinessInfo businessInfo, pw.Font boldFont, pw.Font font, String language) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              businessInfo.name,
              style: pw.TextStyle(font: boldFont, fontSize: 20),
            ),
            if (businessInfo.address != null) ...[
              pw.SizedBox(height: 5),
              pw.Text(
                businessInfo.address!.formattedAddress,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (businessInfo.email != null) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                businessInfo.email!,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (businessInfo.phone != null) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                businessInfo.phone!,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
          ],
        ),
        if (businessInfo.logoData != null)
          pw.Container(
            width: 80,
            height: 80,
            child: pw.Image(
              pw.MemoryImage(businessInfo.logoData!),
              fit: pw.BoxFit.contain,
            ),
          ),
      ],
    );
  }

  pw.Widget _buildInvoiceTitle(Invoice invoice, pw.Font boldFont, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('invoice', language),
          style: pw.TextStyle(font: boldFont, fontSize: 28, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          '${InvoiceLocalizationService.getTranslation('invoice_number', language)}${invoice.invoiceNumber}',
          style: pw.TextStyle(font: boldFont, fontSize: 16),
        ),
      ],
    );
  }

  pw.Widget _buildClientInfo(Client client, pw.Font boldFont, pw.Font font, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('bill_to', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          client.name,
          style: pw.TextStyle(font: boldFont, fontSize: 14),
        ),
        if (client.email != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            client.email!,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
        if (client.address != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            client.address!.formattedAddress,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
        if (client.taxId != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            'Tax ID: ${client.taxId!}',
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
      ],
    );
  }

  pw.Widget _buildInvoiceInfo(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('invoice_details', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        _buildInfoRow(InvoiceLocalizationService.getTranslation('issue_date', language), _formatDate(invoice.issueDate, locale), boldFont, font),
        if (invoice.dueDate != null)
          _buildInfoRow(InvoiceLocalizationService.getTranslation('due_date', language), _formatDate(invoice.dueDate!, locale), boldFont, font),
      ],
    );
  }

  pw.Widget _buildInfoRow(String label, String value, pw.Font boldFont, pw.Font font) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 3),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 100,
            child: pw.Text(
              label,
              style: pw.TextStyle(font: font, fontSize: 11),
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: boldFont, fontSize: 11),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildLineItemsTable(
    Invoice invoice,
    List<TimeEntry>? timeEntries,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    final headers = [
      InvoiceLocalizationService.getTranslation('description', language),
      InvoiceLocalizationService.getTranslation('hours', language),
      InvoiceLocalizationService.getTranslation('rate', language),
      InvoiceLocalizationService.getTranslation('amount', language),
    ];
    final data = <List<String>>[];

    for (final item in invoice.additionalItems) {
      data.add([
        item.description,
        _formatQuantity(item.quantity),
        _formatCurrency(item.rate, invoice.currency, locale),
        _formatCurrency(item.amount, invoice.currency, locale),
      ]);
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
      },
      children: [
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: headers.map((header) =>
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                header,
                style: pw.TextStyle(font: boldFont, fontSize: 12),
              ),
            ),
          ).toList(),
        ),
        ...data.map((row) =>
          pw.TableRow(
            children: row.asMap().entries.map((entry) =>
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  entry.value,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 11,
                  ),
                  textAlign: entry.key == 0 ? pw.TextAlign.left : pw.TextAlign.right,
                ),
              ),
            ).toList(),
          ),
        ),
      ],
    );
  }

  pw.Widget _buildTotalsSection(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 200,
        child: pw.Column(
          children: [
            _buildTotalRow(
              '${InvoiceLocalizationService.getTranslation('subtotal', language)}:',
              _formatCurrency(invoice.subtotal, invoice.currency, locale),
              font,
              font,
            ),
            if (invoice.taxAmount > 0)
              _buildTotalRow(
                '${InvoiceLocalizationService.getTranslation('tax', language)} (${invoice.taxRate.toStringAsFixed(1)}%):',
                _formatCurrency(invoice.taxAmount, invoice.currency, locale),
                font,
                font,
              ),
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 8),
              padding: const pw.EdgeInsets.symmetric(vertical: 8),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  top: pw.BorderSide(width: 2, color: PdfColors.blue800),
                ),
              ),
              child: _buildTotalRow(
                '${InvoiceLocalizationService.getTranslation('total', language)}:',
                _formatCurrency(invoice.total, invoice.currency, locale),
                boldFont,
                boldFont,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  pw.Widget _buildTotalRow(
    String label,
    String value,
    pw.Font labelFont,
    pw.Font valueFont, {
    double fontSize = 12,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(font: labelFont, fontSize: fontSize),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: valueFont, fontSize: fontSize),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildNotesSection(String notes, pw.Font boldFont, pw.Font font, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('notes', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 5),
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            border: pw.Border.all(color: PdfColors.grey200),
          ),
          child: pw.Text(
            notes,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ),
      ],
    );
  }

  pw.Widget _buildFooter(BusinessInfo businessInfo, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300),
        ),
      ),
      child: pw.Column(
        children: [
          if (businessInfo.website != null)
            pw.Text(
              businessInfo.website!,
              style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.blue600),
            ),
          if (businessInfo.taxId != null) ...[
            pw.SizedBox(height: 3),
            pw.Text(
              'Tax ID: ${businessInfo.taxId!}',
              style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600),
            ),
          ],
          pw.SizedBox(height: 5),
          pw.Text(
            InvoiceLocalizationService.getTranslation('thank_you_for_business', language),
            style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildDetailedHeader(BusinessInfo businessInfo, pw.Font boldFont, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: _buildHeader(businessInfo, boldFont, font, language),
    );
  }

  pw.Widget _buildDetailedInvoiceTitle(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        _buildInvoiceTitle(invoice, boldFont, locale, language),
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: pw.BoxDecoration(
            color: _getStatusColor(invoice.status, language),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            _getStatusText(invoice.status, language).toUpperCase(),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 10,
              color: PdfColors.white,
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _buildDetailedClientInfo(Client client, pw.Font boldFont, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: _buildClientInfo(client, boldFont, font, language),
    );
  }

  pw.Widget _buildDetailedInvoiceInfo(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: _buildInvoiceInfo(invoice, boldFont, font, locale, language),
    );
  }

  pw.Widget _buildDetailedTimeEntries(
    List<TimeEntry> timeEntries,
    Invoice invoice,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('time_entries', language),
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(1.5),
            2: const pw.FlexColumnWidth(1),
            3: const pw.FlexColumnWidth(1),
            4: const pw.FlexColumnWidth(1.5),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.blue100),
              children: [
                InvoiceLocalizationService.getTranslation('project', language),
                InvoiceLocalizationService.getTranslation('date', language),
                InvoiceLocalizationService.getTranslation('hours', language),
                InvoiceLocalizationService.getTranslation('rate', language),
                InvoiceLocalizationService.getTranslation('amount', language),
              ].map((header) =>
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    header,
                    style: pw.TextStyle(font: boldFont, fontSize: 11),
                  ),
                ),
              ).toList(),
            ),
          ],
        ),
        pw.SizedBox(height: 20),
      ],
    );
  }

  pw.Widget _buildDetailedLineItems(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('additional_items', language),
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 10),
        _buildLineItemsTable(invoice, null, boldFont, font, locale, language),
      ],
    );
  }

  pw.Widget _buildDetailedTotalsSection(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: _buildTotalsSection(invoice, boldFont, font, locale, language),
    );
  }

  pw.Widget _buildPaymentTerms(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          InvoiceLocalizationService.getTranslation('payment_terms', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          InvoiceLocalizationService.getTranslation('payment_due_within_30_days', language),
          style: pw.TextStyle(font: font, fontSize: 11),
        ),
        if (invoice.dueDate != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            '${InvoiceLocalizationService.getTranslation('due_date', language)}: ${_formatDate(invoice.dueDate!, locale)}',
            style: pw.TextStyle(font: boldFont, fontSize: 11),
          ),
        ],
        pw.SizedBox(height: 10),
      ],
    );
  }

  pw.Widget _buildDetailedFooter(BusinessInfo businessInfo, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: const pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.blue200, width: 2),
        ),
      ),
      child: _buildFooter(businessInfo, font, language),
    );
  }

  pw.Widget _buildSimpleLineItems(
    Invoice invoice,
    List<TimeEntry>? timeEntries,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    return pw.Column(
      children: invoice.additionalItems.map((item) =>
        pw.Padding(
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Text(
                  item.description,
                  style: pw.TextStyle(font: font, fontSize: 12),
                ),
              ),
              pw.Text(
                '${_formatQuantity(item.quantity)} × ${_formatCurrency(item.rate, invoice.currency, locale)}',
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(width: 20),
              pw.Text(
                _formatCurrency(item.amount, invoice.currency, locale),
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
            ],
          ),
        ),
      ).toList(),
    );
  }

  String _formatQuantity(double quantity) {
    if (quantity == quantity.roundToDouble()) {
      return quantity.round().toString();
    } else {
      return quantity.toStringAsFixed(2);
    }
  }

  String _getStatusText(InvoiceStatus status, String language) {
    switch (status) {
      case InvoiceStatus.draft:
        return InvoiceLocalizationService.getTranslation('draft', language);
      case InvoiceStatus.sent:
        return InvoiceLocalizationService.getTranslation('sent', language);
      case InvoiceStatus.paid:
        return InvoiceLocalizationService.getTranslation('paid', language);
      case InvoiceStatus.overdue:
        return InvoiceLocalizationService.getTranslation('overdue', language);
      case InvoiceStatus.cancelled:
        return InvoiceLocalizationService.getTranslation('cancelled', language);
    }
  }

  PdfColor _getStatusColor(InvoiceStatus status, String language) {
    switch (status) {
      case InvoiceStatus.draft:
        return PdfColors.grey500;
      case InvoiceStatus.sent:
        return PdfColors.blue500;
      case InvoiceStatus.paid:
        return PdfColors.green500;
      case InvoiceStatus.overdue:
        return PdfColors.red500;
      case InvoiceStatus.cancelled:
        return PdfColors.orange500;
    }
  }
}

class PDFGenerationException implements Exception {
  final String message;

  const PDFGenerationException(this.message);

  @override
  String toString() => 'PDFGenerationException: $message';
}