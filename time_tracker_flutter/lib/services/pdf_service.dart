import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_models.dart' as pdf_models;

import 'package:time_tracker_flutter/services/pdf/pdf_validator.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_templates.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_file_operations.dart';

/// Main service for generating PDF invoices with professional templates
class PDFService {
  final DatabaseService _databaseService;

  PDFService({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService();

  /// Generate PDF for an invoice with specified template
  Future<Uint8List> generateInvoicePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Uint8List? logoData,
  }) async {
    try {
      final pdf = pw.Document();

      // Load fonts for better text rendering
      final font = await _loadFont();
      final boldFont = await _loadBoldFont();

      // Get language from invoice locale or provided language parameter
      final invoiceLanguage = language ?? invoice.locale;

      // Create parameters object
      final params = pdf_models.PDFGenerationParams(
        invoice: invoice,
        client: client,
        businessInfo: pdf_models.PDFBusinessInfo(
          businessInfo: businessInfo,
          logoData: logoData,
        ),
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: invoiceLanguage,
      );

      // Validate parameters
      PDFValidator.validateTemplateParameters(params);

      // Create template and generate page
      final templateInstance = PDFTemplateFactory.createTemplate(template);
      await templateInstance.generatePage(pdf, params, font, boldFont);

      return await pdf.save();
    } catch (e) {
      throw PDFGenerationException('Failed to generate PDF: $e');
    }
  }

  /// Generate and save PDF to device storage
  Future<String> generateAndSavePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    String? customFileName,
    String? customDirectory,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final pdfData = await generateInvoicePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
      );

      onProgress?.call(0.7);

      final filePath = await PDFFileOperations.generateAndSavePDF(
        pdfData: pdfData,
        invoice: invoice,
        customFileName: customFileName,
        customDirectory: customDirectory,
        onProgress: onProgress,
      );

      onProgress?.call(1.0);
      return filePath;
    } catch (e) {
      throw PDFGenerationException('Failed to save PDF: $e');
    }
  }

  /// Share PDF via system share dialog
  Future<void> shareInvoicePDF({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final filePath = await generateAndSavePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
        onProgress: onProgress,
      );

      onProgress?.call(0.9);

      await PDFFileOperations.shareInvoicePDF(
        filePath: filePath,
        invoice: invoice,
        client: client,
        onProgress: onProgress,
      );

      onProgress?.call(1.0);
    } catch (e) {
      throw PDFGenerationException('Failed to share PDF: $e');
    }
  }

  /// Generate PDF with progress tracking for large invoices
  Future<Uint8List> generateInvoicePDFWithProgress({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      // Validate parameters
      PDFValidator.validatePDFParameters(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
      );

      onProgress?.call(0.3);

      // Generate PDF with progress updates
      final result = await generateInvoicePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
      );

      onProgress?.call(1.0);
      return result;
    } catch (e) {
      throw PDFGenerationException('Failed to generate PDF with progress: $e');
    }
  }

  /// Save PDF to user-selected location
  Future<String?> saveInvoicePDFToCustomLocation({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      // Generate PDF data
      final pdfData = await generateInvoicePDFWithProgress(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
        onProgress: (progress) => onProgress?.call(progress * 0.7),
      );

      onProgress?.call(0.8);

      // Let user select save location
      final result = await PDFFileOperations.saveInvoicePDFToCustomLocation(
        pdfData: pdfData,
        invoice: invoice,
        onProgress: onProgress,
      );

      onProgress?.call(1.0);
      return result;
    } catch (e) {
      throw PDFGenerationException('Failed to save PDF to custom location: $e');
    }
  }

  /// Generate PDF for preview (returns file path)
  Future<String> generatePDFForPreview({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? language,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final filePath = await generateAndSavePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: language,
        onProgress: (progress) => onProgress?.call(progress * 0.9),
      );

      onProgress?.call(1.0);
      return filePath;
    } catch (e) {
      throw PDFGenerationException('Failed to generate PDF for preview: $e');
    }
  }

  /// Share PDF via email with attachment
  Future<void> shareInvoicePDFViaEmail({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
    Locale? locale,
    String? recipientEmail,
    String? customSubject,
    String? customBody,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final filePath = await generateAndSavePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: template,
        timeEntries: timeEntries,
        locale: locale,
        language: locale?.languageCode, // Pass locale language for email
        onProgress: onProgress,
      );

      onProgress?.call(0.9);

      await PDFFileOperations.shareInvoicePDFViaEmail(
        filePath: filePath,
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        locale: locale,
        recipientEmail: recipientEmail,
        customSubject: customSubject,
        customBody: customBody,
        onProgress: onProgress,
      );

      onProgress?.call(1.0);
    } catch (e) {
      throw PDFGenerationException('Failed to share PDF via email: $e');
    }
  }

  /// Export multiple invoices as a batch
  Future<List<String>> exportInvoiceBatch({
    required List<Invoice> invoices,
    required Map<String, Client> clients,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    Map<String, List<TimeEntry>>? timeEntriesMap,
    Locale? locale,
    String? language,
    String? customDirectory,
    Function(double)? onProgress,
  }) async {
    try {
      final pdfDataList = <Uint8List>[];
      final totalInvoices = invoices.length;

      // Generate PDF data for all invoices
      for (int i = 0; i < invoices.length; i++) {
        final invoice = invoices[i];
        final client = clients[invoice.clientId];

        if (client == null) {
          throw PDFGenerationException('Client not found for invoice ${invoice.invoiceNumber}');
        }

        final timeEntries = timeEntriesMap?[invoice.id];

        final pdfData = await generateInvoicePDF(
          invoice: invoice,
          client: client,
          businessInfo: businessInfo,
          template: template,
          timeEntries: timeEntries,
          locale: locale,
          language: language,
        );

        pdfDataList.add(pdfData);
        onProgress?.call((i + 1) / totalInvoices * 0.7);
      }

      // Save all PDFs
      final filePaths = await PDFFileOperations.exportInvoiceBatch(
        pdfDataList: pdfDataList,
        invoices: invoices,
        customDirectory: customDirectory,
        onProgress: (progress) => onProgress?.call(0.7 + progress * 0.3),
      );

      return filePaths;
    } catch (e) {
      throw PDFGenerationException('Failed to export invoice batch: $e');
    }
  }

  /// Load default font with Unicode support
  Future<pw.Font> _loadFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to Helvetica if TTF loading fails
      return pw.Font.helvetica();
    }
  }

  /// Load bold font with Unicode support
  Future<pw.Font> _loadBoldFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to Helvetica Bold if TTF loading fails
      return pw.Font.helveticaBold();
    }
  }

  /// Check if invoice is considered large (for progress tracking)
  bool isLargeInvoice(Invoice invoice, List<TimeEntry>? timeEntries) {
    return PDFValidator.isLargeInvoice(invoice, timeEntries);
  }

  /// Get available export formats
  List<String> getSupportedExportFormats() {
    return ['PDF'];
  }

  /// Clean up temporary PDF files
  Future<void> cleanupTemporaryFiles() async {
    await PDFFileOperations.cleanupTemporaryFiles();
  }

  /// Get PDF file size estimate
  Future<int> estimatePDFSize({
    required Invoice invoice,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
  }) async {
    return await PDFFileOperations.estimatePDFSize(
      invoice: invoice,
      businessInfo: businessInfo,
      template: template,
      timeEntries: timeEntries,
    );
  }
}

/// Exception thrown when PDF generation fails
class PDFGenerationException implements Exception {
  final String message;

  const PDFGenerationException(this.message);

  @override
  String toString() => 'PDFGenerationException: $message';
}

